# Implement N-ary Tree Support for Categorical Features

## Context
Mulberri currently uses binary splits for all features, including categorical ones. This creates unnecessarily deep, unbalanced trees when categorical features have many values (e.g., US states, job titles), severely impacting interpretability - one of <PERSON><PERSON><PERSON><PERSON>'s core goals.

## Problem description
For categorical features with high cardinality, binary splits create:

Deep trees: 50 nested nodes instead of 1 layer with 50 branches for "US States"
Unbalanced structure: Each node splits "1 value vs all others"
Poor interpretability: Complex nested decisions instead of intuitive categorical choices
Performance issues: Excessive tree depth slows prediction


## Technical investigation
- CategoricalSplitEvaluator generates multiple binary candidates per categorical feature
- DecisionNode structure only supports binary children (left/right)
- No mechanism exists to create single decision node with multiple categorical branches
- CLI flag --binary-splits-only exists but n-ary logic not implemented

## Acceptance criteria
[] Add n-ary split evaluation for categorical features when --binary-splits-only=false
Update DecisionNode structure to support map[interface{}]Node children
Create NAryCategoricalSplitEvaluator that generates single split with multiple outcomes
Modify SplitCandidate to include ChildPartitions map[interface{}][]int
Update ApplySplitToView to handle multiple child views instead of just left/right
Implement tree building logic that respects the CLI flag
Update prediction traversal to handle n-ary navigation
Maintain backward compatibility with existing binary split behavior
Add tests comparing tree depth: binary vs n-ary for high-cardinality features
Update JSON serialization to support n-ary tree structures
