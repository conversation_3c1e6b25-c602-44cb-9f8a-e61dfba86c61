// Package training provides split evaluation functionality for decision trees.
//
// This module implements the Strategy pattern for different split evaluation
// algorithms, supporting both numerical (int/float) and categorical (string)
// features with unified interfaces.
//
// Design Principles:
// - Strategy pattern for different feature types
// - View-based operations for memory efficiency
// - Generic interfaces for different target types
// - Type-safe split candidate generation
package training

import (
	"fmt"
	"sort"

	"github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/data/features"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// SplitType represents the type of split operation.
type SplitType int

const (
	// NumericalSplit uses threshold-based splitting for int/float features
	NumericalSplit SplitType = iota
	// CategoricalSplit uses value-based splitting for string features
	CategoricalSplit
)

// SplitCandidate represents a potential split for any feature type.
//
// Contains all information needed to evaluate and apply a split,
// including the split condition, quality metrics, and resulting subset sizes.
//
// Performance: Lightweight struct for efficient candidate comparison
// Relationships: Generated by SplitEvaluator, consumed by tree builder
// Side effects: None (immutable data structure)
//
// Example:
//
//	// Numerical split: "age <= 30.5"
//	candidate := SplitCandidate{
//	    FeatureName: "age",
//	    Type:        NumericalSplit,
//	    Threshold:   &threshold,
//	    Gain:        0.23,
//	    LeftSize:    15,
//	    RightSize:   10,
//	}
type SplitCandidate struct {
	FeatureName string    // Name of the feature being split
	Type        SplitType // Type of split (numerical or categorical)

	// For numerical splits (int/float): "feature <= threshold"
	Threshold *float64 // Split threshold value

	// For categorical splits (string): "feature == value"
	Value interface{} // Split value for categorical features

	Gain      float64 // Information gain from this split
	LeftSize  int     // Number of samples in left child
	RightSize int     // Number of samples in right child
}

// SplitEvaluator defines the interface for evaluating splits on different feature types.
//
// Generic interface supporting different target types and feature types.
// Implementations handle the specifics of numerical vs categorical splitting.
//
// Args:
// - view: DatasetView containing the subset of data to split
// - column: FeatureColumn interface for accessing feature values
// - baseImpurity: Current impurity of the node before splitting
// - featureName: Name of the feature being evaluated
//
// Returns: Slice of split candidates ordered by quality (best first)
// Performance: O(n log n) for numerical features, O(n*k) for categorical
// Relationships: Used by tree builder to find best splits
// Side effects: None (read-only operations on view)
//
// Example:
//
//	evaluator := factory.CreateEvaluator(column)
//	candidates, err := evaluator.EvaluateSplits(view, column, 0.918, "age")
type SplitEvaluator[T comparable] interface {
	EvaluateSplits(view *dataset.DatasetView[T], column dataset.FeatureColumn, baseImpurity float64, featureName string) []SplitCandidate
}

// numericalPair represents a (value, target) pair for numerical split evaluation.
//
// Used internally by NumericalSplitEvaluator to sort feature values
// and efficiently evaluate all possible threshold splits.
//
// Performance: Lightweight struct for sorting operations
// Relationships: Internal to NumericalSplitEvaluator
type numericalPair[T comparable] struct {
	value  float64 // Feature value converted to float64
	target T       // Target value for this sample
}

// NumericalSplitEvaluator handles threshold-based splits for int/float features.
//
// Implements the standard decision tree splitting algorithm for numerical features:
// 1. Sort all feature values
// 2. Evaluate split at midpoint between each adjacent pair
// 3. Calculate information gain for each potential split
// 4. Return candidates sorted by gain
//
// Performance: O(n log n) due to sorting, where n is number of samples
// Relationships: Created by SplitEvaluatorFactory for numerical features
// Side effects: None (operates on view data without modification)
//
// Example:
//
//	evaluator := &NumericalSplitEvaluator[string]{impurityCalc: entropyCalc}
//	candidates, _ := evaluator.EvaluateSplits(view, ageColumn, 0.918)
type NumericalSplitEvaluator[T comparable] struct{}

// EvaluateSplits finds the best threshold-based splits for a numerical feature.
//
// Algorithm:
// 1. Extract and sort (value, target) pairs from the view
// 2. Initialize left/right target distributions
// 3. For each potential threshold between adjacent values:
//   - Update distributions by moving samples from right to left
//   - Calculate weighted impurity and information gain
//   - Create split candidate if gain is positive
//
// 4. Return all candidates (caller selects best)
//
// Args:
// - view: DatasetView containing active samples for this node
// - column: FeatureColumn for accessing numerical feature values
// - baseImpurity: Current node impurity before splitting
// - featureName: Name of the feature being evaluated
//
// Returns: Slice of split candidates, empty if no valid splits found
// Constraints: Feature must be numerical (int/float), view must not be empty
// Performance: O(n log n) for sorting + O(n) for evaluation
// Relationships: Core algorithm for numerical feature splitting
// Side effects: None (read-only operations)
//
// Example:
//
//	// For age feature with values [25, 30, 35, 40] and targets ["young", "old", "old", "old"]
//	// Might generate candidates for thresholds: 27.5, 32.5, 37.5
//	candidates, err := evaluator.EvaluateSplits(view, ageColumn, 1.0, "age")
func (n *NumericalSplitEvaluator[T]) EvaluateSplits(
	view *dataset.DatasetView[T],
	column dataset.FeatureColumn,
	baseImpurity float64,
	featureName string,
) []SplitCandidate {

	// Get sorted pairs from ONLY the active indices in the view
	sortedPairs := n.getSortedNumericalPairs(view, featureName)
	if sortedPairs == nil {
		logger.Warn(fmt.Sprintf("Failed to get sorted pairs for feature %s", featureName))
		return []SplitCandidate{}
	}

	if len(sortedPairs) < 2 {
		return []SplitCandidate{} // Need at least 2 samples to split
	}

	var candidates []SplitCandidate

	// Initialize target distributions for the VIEW's data only
	leftDist := make(map[T]int)
	rightDist := make(map[T]int)

	// All samples from VIEW start in right distribution
	for _, pair := range sortedPairs {
		rightDist[pair.target]++
	}

	// Evaluate each potential split point
	for i := 0; i < len(sortedPairs)-1; i++ {
		// Move current sample from right to left
		target := sortedPairs[i].target
		leftDist[target]++
		rightDist[target]--
		if rightDist[target] == 0 {
			delete(rightDist, target)
		}

		// Create split candidate if values are different
		if sortedPairs[i].value != sortedPairs[i+1].value {
			threshold := (sortedPairs[i].value + sortedPairs[i+1].value) / 2

			leftSize := i + 1
			rightSize := len(sortedPairs) - leftSize

			leftImpurity := CalculateEntropy(leftDist, leftSize)
			rightImpurity := CalculateEntropy(rightDist, rightSize)

			totalSize := len(sortedPairs)
			weightedImpurity := (float64(leftSize)*leftImpurity + float64(rightSize)*rightImpurity) / float64(totalSize)
			gain := baseImpurity - weightedImpurity

			if gain > 0 { // Only include splits that improve purity
				candidates = append(candidates, SplitCandidate{
					FeatureName: featureName,
					Type:        NumericalSplit,
					Threshold:   &threshold,
					Gain:        gain,
					LeftSize:    leftSize,
					RightSize:   rightSize,
				})
			}
		}
	}

	return candidates
}

// getSortedNumericalPairs extracts and sorts (value, target) pairs from the view.
//
// Args:
// - view: DatasetView containing active samples
// - column: FeatureColumn for accessing feature values
// - featureName: Name of the feature being accessed
//
// Returns: Sorted slice of numericalPair structs
// Performance: O(n log n) due to sorting
// Relationships: Helper method for EvaluateSplits
// Side effects: None (read-only operations)
func (n *NumericalSplitEvaluator[T]) getSortedNumericalPairs(
	view *dataset.DatasetView[T],
	featureName string,
) []numericalPair[T] {

	var pairs []numericalPair[T]

	// Extract (value, target) pairs from VIEW's active indices only
	for i := 0; i < view.GetSize(); i++ {
		value := view.GetFeatureValue(i, featureName)
		if value == nil {
			continue // Skip missing values
		}

		target := view.GetTarget(i)

		// Convert value to float64 for numerical comparison
		var floatValue float64
		switch v := value.(type) {
		case *int64:
			floatValue = float64(*v)
		case *float64:
			floatValue = *v
		default:
			continue // Skip non-numerical values
		}

		pairs = append(pairs, numericalPair[T]{
			value:  floatValue,
			target: target,
		})
	}

	// Sort by numerical value
	sort.Slice(pairs, func(i, j int) bool {
		return pairs[i].value < pairs[j].value
	})

	return pairs
}

// CategoricalSplitEvaluator handles value-based splits for string features.
//
// Implements categorical splitting algorithm for string features:
// 1. Build joint distribution of (feature_value, target) pairs
// 2. For each unique feature value, evaluate binary split (value vs all others)
// 3. Calculate information gain for each potential split
// 4. Return candidates sorted by gain
//
// Performance: O(n*k) where n is samples and k is unique feature values
// Relationships: Created by SplitEvaluatorFactory for string features
// Side effects: None (operates on view data without modification)
//
// Example:
//
//	evaluator := &CategoricalSplitEvaluator[string]{impurityCalc: entropyCalc}
//	candidates, _ := evaluator.EvaluateSplits(view, educationColumn, 0.918)
type CategoricalSplitEvaluator[T comparable] struct{}

// EvaluateSplits finds the best value-based splits for a categorical feature.
//
// Algorithm:
// 1. Build joint distribution of (feature_value, target_value) from view
// 2. For each unique feature value:
//   - Create binary split: samples with this value vs all others
//   - Calculate left distribution (this value) and right distribution (others)
//   - Calculate weighted impurity and information gain
//   - Create split candidate if gain is positive
//
// 3. Return all candidates (caller selects best)
//
// Args:
// - view: DatasetView containing active samples for this node
// - column: FeatureColumn for accessing categorical feature values
// - baseImpurity: Current node impurity before splitting
// - featureName: Name of the feature being evaluated
//
// Returns: Slice of split candidates, empty if no valid splits found
// Constraints: Feature must be categorical (string), view must not be empty
// Performance: O(n*k) where n is samples and k is unique feature values
// Relationships: Core algorithm for categorical feature splitting
// Side effects: None (read-only operations)
//
// Example:
//
//	// For education feature with values ["college", "high_school", "college", "graduate"]
//	// Might generate candidates for: "college" vs others, "high_school" vs others, "graduate" vs others
//	candidates, err := evaluator.EvaluateSplits(view, educationColumn, 1.0, "education")
func (c *CategoricalSplitEvaluator[T]) EvaluateSplits(
	view *dataset.DatasetView[T],
	column dataset.FeatureColumn,
	baseImpurity float64,
	featureName string,
) []SplitCandidate {

	// Get joint distribution from VIEW's active indices only
	jointDist := c.getStringJointDistribution(view, featureName)
	if jointDist == nil {
		logger.Warn(fmt.Sprintf("Failed to get joint distribution for feature %s", featureName))
		return []SplitCandidate{}
	}

	if len(jointDist) < 2 {
		return []SplitCandidate{} // Need at least 2 unique values to split
	}

	var candidates []SplitCandidate

	// Evaluate each unique feature value as a potential split
	for splitValue := range jointDist {
		leftDist := jointDist[splitValue]                                     // Samples with this feature value
		rightDist := c.calculateComplementDistribution(jointDist, splitValue) // All other samples

		leftSize := c.sumDistribution(leftDist)
		rightSize := c.sumDistribution(rightDist)

		if leftSize == 0 || rightSize == 0 {
			continue // Skip splits that don't actually divide the data
		}

		leftImpurity := CalculateEntropy(leftDist, leftSize)
		rightImpurity := CalculateEntropy(rightDist, rightSize)

		totalSize := leftSize + rightSize
		weightedImpurity := (float64(leftSize)*leftImpurity + float64(rightSize)*rightImpurity) / float64(totalSize)
		gain := baseImpurity - weightedImpurity

		if gain > 0 { // Only include splits that improve purity
			candidates = append(candidates, SplitCandidate{
				FeatureName: featureName,
				Type:        CategoricalSplit,
				Value:       splitValue,
				Gain:        gain,
				LeftSize:    leftSize,
				RightSize:   rightSize,
			})
		}
	}

	return candidates
}

// getStringJointDistribution builds joint distribution of (feature_value, target_value) pairs.
//
// Args:
// - view: DatasetView containing active samples
// - column: FeatureColumn for accessing feature values
// - featureName: Name of the feature being accessed
//
// Returns: Map[feature_value]Map[target_value]count
// Performance: O(n) where n is number of samples in view
// Relationships: Helper method for EvaluateSplits
// Side effects: None (read-only operations)
func (c *CategoricalSplitEvaluator[T]) getStringJointDistribution(
	view *dataset.DatasetView[T],
	featureName string,
) map[interface{}]map[T]int {

	jointDist := make(map[interface{}]map[T]int)

	// Build joint distribution from VIEW's active indices only
	for i := 0; i < view.GetSize(); i++ {
		value := view.GetFeatureValue(i, featureName)
		if value == nil {
			continue // Skip missing values
		}

		target := view.GetTarget(i)

		// Extract string value
		var stringValue string
		switch v := value.(type) {
		case *string:
			stringValue = *v
		default:
			continue // Skip non-string values
		}

		if jointDist[stringValue] == nil {
			jointDist[stringValue] = make(map[T]int)
		}
		jointDist[stringValue][target]++
	}

	return jointDist
}

// calculateComplementDistribution calculates target distribution for all values != splitValue.
//
// Args:
// - jointDist: Joint distribution of (feature_value, target_value) pairs
// - splitValue: Feature value to exclude from the complement
//
// Returns: Target distribution for all samples NOT having splitValue
// Performance: O(k*t) where k is unique feature values and t is unique targets
// Relationships: Helper method for EvaluateSplits
func (c *CategoricalSplitEvaluator[T]) calculateComplementDistribution(
	jointDist map[interface{}]map[T]int,
	splitValue interface{},
) map[T]int {
	rightDist := make(map[T]int)

	for featureValue, targetCounts := range jointDist {
		if featureValue != splitValue {
			for target, count := range targetCounts {
				rightDist[target] += count
			}
		}
	}

	return rightDist
}

// sumDistribution calculates total count across all target values.
//
// Args:
// - distribution: Map of target values to their counts
//
// Returns: Sum of all counts in the distribution
// Performance: O(t) where t is number of unique target values
// Relationships: Helper method for calculating subset sizes
func (c *CategoricalSplitEvaluator[T]) sumDistribution(distribution map[T]int) int {
	total := 0
	for _, count := range distribution {
		total += count
	}
	return total
}

// SplitEvaluatorFactory creates appropriate split evaluators based on feature type.
//
// Implements the Factory pattern to abstract the creation of different
// split evaluator types based on feature characteristics.
//
// Performance: O(1) factory method
// Relationships: Used by tree builder to create evaluators for each feature
// Side effects: Allocates new evaluator instances
//
// Example:
//
//	factory := &SplitEvaluatorFactory[string]{impurityCalc: entropyCalc}
//	evaluator := factory.CreateEvaluator(column.GetType())
type SplitEvaluatorFactory[T comparable] struct{}

// CreateEvaluator creates the appropriate split evaluator for the given feature type.
//
// Args:
// - featureType: Type of the feature (IntegerFeature, FloatFeature, StringFeature)
//
// Returns: SplitEvaluator implementation or nil for unsupported types
// Constraints: Only supports IntegerFeature, FloatFeature, and StringFeature
// Performance: O(1) switch statement
// Relationships: Maps feature types to evaluator implementations
// Side effects: Allocates new evaluator instance
//
// Example:
//
//	evaluator := factory.CreateEvaluator(features.IntegerFeature) // Returns NumericalSplitEvaluator
//	evaluator := factory.CreateEvaluator(features.StringFeature)  // Returns CategoricalSplitEvaluator
func (f *SplitEvaluatorFactory[T]) CreateEvaluator(featureType features.FeatureType) SplitEvaluator[T] {
	switch featureType {
	case features.IntegerFeature, features.FloatFeature:
		// BOTH int and float use numerical splitting
		return &NumericalSplitEvaluator[T]{}
	case features.StringFeature:
		return &CategoricalSplitEvaluator[T]{}
	default:
		return nil // Unsupported feature type
	}
}

// NewSplitEvaluatorFactory creates a new factory for split evaluators.
//
// Returns: New factory instance ready to create evaluators
// Performance: O(1) factory creation
// Relationships: Used by training configuration to create factory
// Side effects: Allocates factory structure
//
// Example:
//
//	factory := NewSplitEvaluatorFactory[string]()
func NewSplitEvaluatorFactory[T comparable]() *SplitEvaluatorFactory[T] {
	return &SplitEvaluatorFactory[T]{}
}
