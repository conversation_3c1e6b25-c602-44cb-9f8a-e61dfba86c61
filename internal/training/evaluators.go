// Package training provides split evaluation functionality for decision trees.
//
// This module focuses specifically on evaluating potential splits for different
// feature types, providing the core splitting logic without tree building overhead.
//
// Design Principles:
// - Focused on split evaluation only
// - Support for numerical and categorical features
// - Memory-efficient view-based operations
// - Clean separation of concerns
package training

import (
	"fmt"

	"github.com/berrijam/mulberri/internal/data/dataset"
	"github.com/berrijam/mulberri/internal/data/features"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// SplitEvaluationResult contains the results of evaluating splits for a feature.
//
// Provides comprehensive information about potential splits including
// the best candidate and all evaluated options for analysis.
//
// Performance: Lightweight result container
// Relationships: Returned by split evaluation functions
// Side effects: None (immutable result)
type SplitEvaluationResult struct {
	BestCandidate *SplitCandidate      // Best split found (highest gain)
	AllCandidates []SplitCandidate     // All evaluated split candidates
	FeatureName   string               // Name of the feature evaluated
	FeatureType   features.FeatureType // Type of the feature evaluated
	BaseImpurity  float64              // Original impurity before splitting
}

// EvaluateFeatureSplits evaluates all possible splits for a single feature.
//
// Main entry point for split evaluation. Automatically determines the appropriate
// evaluation strategy based on feature type and returns comprehensive results.
//
// Args:
// - view: DatasetView containing samples to evaluate
// - dataset: Full dataset for accessing feature columns
// - featureName: Name of the feature to evaluate
// - impurityCalc: Calculator for impurity measures
//
// Returns: SplitEvaluationResult with best split and analysis data
// Constraints: Feature must exist in dataset, view must not be empty
// Performance: O(n log n) for numerical, O(n*k) for categorical features
// Relationships: Coordinates feature-specific evaluation strategies
// Side effects: None (read-only evaluation)
//
// Example:
//
//	entropyCalc := &EntropyCalculator[string]{}
//	result, err := EvaluateFeatureSplits(view, dataset, "age", entropyCalc)
//	if err == nil && result.BestCandidate != nil {
//	    fmt.Printf("Best split: %s with gain %.3f", result.FeatureName, result.BestCandidate.Gain)
//	}
func EvaluateFeatureSplits[T comparable](
	view *dataset.DatasetView[T],
	dataset *dataset.Dataset[T],
	featureName string,
) *SplitEvaluationResult {

	// Get the feature column
	column := dataset.GetColumn(featureName)
	if column == nil {
		logger.Error(fmt.Sprintf("Feature column not found: %s", featureName))
		return nil
	}

	// Calculate base impurity using entropy
	targetDist := view.GetTargetDistribution()
	baseImpurity := CalculateEntropy(targetDist, view.GetSize())

	// Create appropriate evaluator based on feature type
	factory := NewSplitEvaluatorFactory[T]()
	evaluator := factory.CreateEvaluator(column.GetType())
	if evaluator == nil {
		logger.Error(fmt.Sprintf("Unsupported feature type for %s", featureName))
		return nil
	}

	// Evaluate splits
	candidates := evaluator.EvaluateSplits(view, column, baseImpurity, featureName)
	if candidates == nil {
		logger.Warn(fmt.Sprintf("No split candidates found for feature %s", featureName))
		candidates = []SplitCandidate{}
	}

	// Find best candidate
	var bestCandidate *SplitCandidate
	for i := range candidates {
		candidate := &candidates[i]
		if bestCandidate == nil || candidate.Gain > bestCandidate.Gain {
			bestCandidate = candidate
		}
	}

	return &SplitEvaluationResult{
		BestCandidate: bestCandidate,
		AllCandidates: candidates,
		FeatureName:   featureName,
		FeatureType:   column.GetType(),
		BaseImpurity:  baseImpurity,
	}
}

// EvaluateAllFeatureSplits evaluates splits for multiple features and returns the best overall.
//
// Convenience function that evaluates splits across multiple features and
// identifies the single best split candidate across all features.
//
// Args:
// - view: DatasetView containing samples to evaluate
// - dataset: Full dataset for accessing feature columns
// - featureNames: Names of features to evaluate
// - impurityCalc: Calculator for impurity measures
//
// Returns: SplitEvaluationResult for the best feature, or nil if no good splits
// Constraints: At least one feature must exist, view must not be empty
// Performance: O(m*n*log(n)) where m=features, n=samples
// Relationships: Orchestrates evaluation across multiple features
// Side effects: None (read-only evaluation)
//
// Example:
//
//	features := []string{"age", "salary", "education"}
//	result, err := EvaluateAllFeatureSplits(view, dataset, features, entropyCalc)
//	if result != nil && result.BestCandidate != nil {
//	    fmt.Printf("Best overall split: %s", result.FeatureName)
//	}
func EvaluateAllFeatureSplits[T comparable](
	view *dataset.DatasetView[T],
	dataset *dataset.Dataset[T],
	featureNames []string,
) *SplitEvaluationResult {

	var bestResult *SplitEvaluationResult

	// Evaluate each feature
	for _, featureName := range featureNames {
		result := EvaluateFeatureSplits(view, dataset, featureName)
		if result == nil {
			logger.Debug(fmt.Sprintf("Skipping feature %s due to evaluation error", featureName))
			continue // Skip features with errors
		}

		// Track best overall result
		if result.BestCandidate != nil {
			if bestResult == nil || result.BestCandidate.Gain > bestResult.BestCandidate.Gain {
				bestResult = result
			}
		}
	}

	return bestResult
}

// ApplySplitToView applies a split candidate to create left and right child views.
//
// Takes a split candidate and partitions the samples in the view according
// to the split condition, creating two new views for further processing.
//
// Args:
// - view: Parent DatasetView to split
// - dataset: Full dataset for accessing feature values
// - split: SplitCandidate containing split information
//
// Returns: Left and right child DatasetViews
// Constraints: Split must be valid, feature must exist in dataset
// Performance: O(n) where n is number of samples in view
// Relationships: Bridges split evaluation and view partitioning
// Side effects: Creates new DatasetView instances
//
// Example:
//
//	leftView, rightView, err := ApplySplitToView(parentView, dataset, bestSplit)
//	if err == nil {
//	    fmt.Printf("Left: %d samples, Right: %d samples", leftView.GetSize(), rightView.GetSize())
//	}
func ApplySplitToView[T comparable](
	view *dataset.DatasetView[T],
	dataset *dataset.Dataset[T],
	split *SplitCandidate,
) (*dataset.DatasetView[T], *dataset.DatasetView[T]) {

	var leftIndices, rightIndices []int

	// Get the feature column
	column := dataset.GetColumn(split.FeatureName)
	if column == nil {
		logger.Error(fmt.Sprintf("Feature column not found: %s", split.FeatureName))
		return nil, nil
	}

	// Partition samples based on split type
	for i := 0; i < view.GetSize(); i++ {
		value := view.GetFeatureValue(i, split.FeatureName)
		if value == nil {
			continue // Skip missing values
		}

		goesLeft := false
		switch split.Type {
		case NumericalSplit:
			if split.Threshold != nil {
				if numValue := extractNumericalValue(value); numValue != nil {
					goesLeft = *numValue <= *split.Threshold
				}
			}
		case CategoricalSplit:
			if strValue := extractStringValue(value); strValue != nil {
				goesLeft = *strValue == split.Value
			}
		}

		// Get physical index for this logical index
		physicalIndex := view.GetActiveIndices()[i]
		if goesLeft {
			leftIndices = append(leftIndices, physicalIndex)
		} else {
			rightIndices = append(rightIndices, physicalIndex)
		}
	}

	// Create child views
	leftView := dataset.CreateView(leftIndices)
	rightView := dataset.CreateView(rightIndices)

	return leftView, rightView
}

// extractNumericalValue safely extracts a numerical value from an interface.
//
// Helper function for converting interface{} values to float64 for
// numerical split evaluation.
//
// Args:
// - value: Interface value to convert
//
// Returns: Pointer to float64 value or nil if conversion fails
// Performance: O(1) type assertion
// Relationships: Helper for numerical split application
// Side effects: None (pure function)
func extractNumericalValue(value interface{}) *float64 {
	switch v := value.(type) {
	case *int64:
		if v != nil {
			result := float64(*v)
			return &result
		}
	case *float64:
		return v
	}
	return nil
}

// extractStringValue safely extracts a string value from an interface.
//
// Helper function for converting interface{} values to string for
// categorical split evaluation.
//
// Args:
// - value: Interface value to convert
//
// Returns: Pointer to string value or nil if conversion fails
// Performance: O(1) type assertion
// Relationships: Helper for categorical split application
// Side effects: None (pure function)
func extractStringValue(value interface{}) *string {
	switch v := value.(type) {
	case *string:
		return v
	}
	return nil
}

// GetSplitDescription returns a human-readable description of a split.
//
// Utility function for logging and debugging split candidates.
//
// Args:
// - split: SplitCandidate to describe
//
// Returns: String description of the split condition
// Performance: O(1) string formatting
// Relationships: Utility for debugging and logging
// Side effects: None (pure function)
//
// Example:
//
//	desc := GetSplitDescription(split)
//	// Returns: "age <= 30.5" or "education == 'college'"
func GetSplitDescription(split *SplitCandidate) string {
	if split == nil {
		return "no split"
	}

	switch split.Type {
	case NumericalSplit:
		if split.Threshold != nil {
			return fmt.Sprintf("%s <= %.3f", split.FeatureName, *split.Threshold)
		}
	case CategoricalSplit:
		return fmt.Sprintf("%s == '%v'", split.FeatureName, split.Value)
	}

	return fmt.Sprintf("%s (unknown split type)", split.FeatureName)
}
